<?php

namespace App\Filament\Admin\Resources\SubscriptionResource\RelationManagers;

use App\Constants\TransactionStatus;
use App\Mapper\TransactionStatusMapper;
use App\Models\Transaction;
use App\Services\InvoiceService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Filament\Tables\Columns\Layout\Panel;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\View;
use Illuminate\Database\Eloquent\Builder;

class TransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'transactions';

    private InvoiceService $invoiceService;
    public function boot(InvoiceService $invoiceService)
    {
        $this->invoiceService = $invoiceService;
    }
    public function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading(__('Invoices & Receipts'))
            ->recordTitleAttribute('invoice_number')
            ->columns([
                Split::make([
                    Tables\Columns\TextColumn::make('invoice_number')
                        ->label(__('Invoice'))
                        ->html()
                        ->searchable(['invoice_number', 'payment_provider_reference'])
                        ->formatStateUsing(function (Transaction $record) {
                            $html = '<div class="space-y-1">';
                            $html .= '<div class="font-medium">' . ($record->invoice_number ?? '-') . '</div>';
                            if ($record->payment_provider_reference) {
                                $html .= '<div class="text-xs text-gray-500 italic">' . $record->payment_provider_reference . '</div>';
                            }
                            $html .= '</div>';
                            return $html;
                        })
                        ->copyable()
                        ->copyableState(fn (Transaction $record) => $record->invoice_number ?? '')
                        ->copyMessage(__('Invoice number copied'))
                        ->copyMessageDuration(1500),

                    Tables\Columns\TextColumn::make('subscription.plan.name')
                        ->label(__('Subscription'))
                        ->searchable()
                        ->sortable(),

                    Tables\Columns\TextColumn::make('amount')
                        ->label(__('Amount'))
                        ->formatStateUsing(function (string $state, Transaction $record) {
                            return money(abs($state), $record->currency->code);
                        })
                        ->sortable(),

                    Tables\Columns\TextColumn::make('status')
                        ->label(__('Status'))
                        ->badge()
                        ->color(fn (Transaction $record, TransactionStatusMapper $mapper): string => $mapper->mapColor($record->status))
                        ->formatStateUsing(fn (string $state, TransactionStatusMapper $mapper): string => $mapper->mapForDisplay($state))
                        ->sortable(),

                    Tables\Columns\TextColumn::make('created_at')
                        ->label(__('Date'))
                        ->dateTime(config('app.datetime_format'))
                        ->sortable()
                        ->searchable(),
                    Tables\Columns\TextColumn::make('uuid')
                        ->label(__('Actions'))
                        ->html()
                        
                        ->formatStateUsing(function (Transaction $record) {
                            // View invoice button
                            $html = '<button>View Invoice</button>';
                            return $html;
                        })
                        ->action(
                            Action::make('generate_invoice')
                            ->visible(fn (Transaction $record, InvoiceService $invoiceService): bool => $invoiceService->canGenerateInvoices($record))
                            ->modalHeading(__('Customize Invoice Details'))
                            ->modalDescription(__('Please review and customize the invoice details before generating the document.'))
                            ->modalSubmitActionLabel(__('Generate Invoice'))
                            ->mountUsing(function (Form $form, array $arguments, Transaction $record) {

                                $customerJson = $record->customer_json;
                                $form->fill([
                                    'email' =>  $customerJson['email'] ?? '',
                                    'displayed_name' =>  $this->invoiceService->getInvoiceCustomerName($customerJson),
                                    'phone' => $customerJson['address']['phone'] ?? '',
                                    'address_line_1' => $customerJson['address']['address_line_1'] ?? '',
                                    'address_line_2' =>  $customerJson['address']['address_line_2'] ?? '',
                                    'city' =>  $customerJson['address']['city'] ?? '',
                                    'country' => isset($customerJson['address']['country']) ?   $customerJson['address']['country']['name'] : '',
                                ]);
                            })
                            ->form([
                                Forms\Components\Section::make(__('Contact Information'))
                                    ->schema([
                                        Forms\Components\TextInput::make('email')
                                            ->label(__('Email'))
                                            ->email() ,

                                        Forms\Components\TextInput::make('displayed_name')
                                            ->label(__('Displayed Name')) ,

                                    ])
                                    ->columns(2),

                                Forms\Components\Section::make(__('Address Information'))
                                    ->schema([
                                        Forms\Components\TextInput::make('address_line_1')
                                            ->label(__('Address Line 1')),

                                        Forms\Components\TextInput::make('address_line_2')
                                            ->label(__('Address Line 2')),

                                        Forms\Components\TextInput::make('city')
                                            ->label(__('City')),

                                        Forms\Components\TextInput::make('country')
                                            ->label(__('Country'))
                                    ])->columns(2),
                            ])
                            ->action(function (array $data, Transaction $record) {
                                // redirects to generate invoice with the form values as json in the query string
                                return redirect()->route('invoice_receipt.generate', [
                                    'docType' => 'invoice',
                                    'uuid' => $record->uuid,
                                    'customData' => json_encode($data)
                                ]);
                            }),
                        )
                        ->grow(false)
                        ->alignEnd(),
                ]),

                Panel::make([
                    View::make('filament.admin.transaction-receipts-collapsible-component')
                ])->collapsible()
                ->visible(fn (Transaction $record): bool => $record->receipts()->count() > 0),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options([
                        TransactionStatus::SUCCESS->value => __('Success'),
                        TransactionStatus::FAILED->value => __('Failed'),
                        TransactionStatus::PENDING->value => __('Pending'),
                        TransactionStatus::REFUNDED->value => __('Refunded'),
                        TransactionStatus::DISPUTED->value => __('Disputed'),
                    ]),
            ])
            ->headerActions([])
            ->actions([

            ])
            ->bulkActions([])
            ->modifyQueryUsing(fn (Builder $query) => $query->with([
                'currency',
                'paymentProvider',
                'receipts',
                'subscription.plan',
            ]))
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading(__('No transactions found'))
            ->emptyStateDescription(__('This subscription has no associated transactions yet.'))
            ->emptyStateIcon('heroicon-o-document-text');
    }


}
